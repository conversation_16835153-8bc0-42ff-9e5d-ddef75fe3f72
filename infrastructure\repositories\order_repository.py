import mysql.connector
from os import getenv
from typing import List, Dict, Any, Optional, Tuple
from uuid import uuid4
import logging

logger = logging.getLogger(__name__)

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class OrderRepository:
    """Repository for order database operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        

    
    def get_expired_order_sections(self) -> List[int]:
        """
        Get all sections with expired orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting expired order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def get_employee_order_sections(self) -> List[int]:
        """
        Get all sections with employee orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting employee order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def create_employee_delivery_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee delivery.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            pickup_pin = generate_pin()
            
            if pickup_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, pickup_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_deliver', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), pickup_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "pickup_pin": pickup_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee delivery reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    
    def create_employee_send_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee sending order.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            insert_pin = generate_pin()
            
            if insert_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, insert_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_send', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), insert_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "insert_pin": insert_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee send reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    
    def find_reservation_by_pickup_pin(self, pickup_pin: str) -> Optional[Dict[str, Any]]:
        """
        Find order reservation by pickup PIN.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT * FROM order_reservations 
                WHERE pickup_pin = %s AND status = 1 AND expired = 0
            """
            cursor.execute(query, (pickup_pin,))
            return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"Error finding reservation by pickup PIN: {e}")
            return None
        finally:
            cursor.close()
            db.close()
    
    def update_reservation(
        self,
        reservation_id: int = None,
        section_id: int = None,
        phone_number: str = None,
        status: int = None,
        new_section_id: int = None,
        return_uuids: bool = False,
        order_number: str = None,
        ) -> dict:
        """
        Unified method to update order reservations with flexible parameters.

        Args:
            reservation_id: Update reservations with ___ reservation_id
            section_id:     Update reservations with ___ section_id
            phone_number:   Update reservations with ___ phone_number
            status:         New status to set
            new_section_id: New section ID to set (for moving reservations)
            return_uuids:   Whether to return UUIDs of updated records
            order_number:   Update reservations for specific order number

        Returns:
            Dict with success status, count of updated records, and optionally UUIDs
        """
        db = get_db()
        cursor = db.cursor()
        try:
            # Build WHERE clause based on provided parameters
            where_conditions = []
            where_params = []

            if reservation_id is not None:
                where_conditions.append("id = %s")
                where_params.append(reservation_id)

            if section_id is not None:
                where_conditions.append("section_id = %s")
                where_params.append(str(section_id))

            if phone_number is not None:
                where_conditions.append("phone_number = %s")
                where_params.append(phone_number)

            if order_number is not None:
                where_conditions.append("order_number = %s")
                where_params.append(order_number)

            # Always exclude expired reservations unless updating by reservation_id
            if reservation_id is None:
                where_conditions.append("expired = 0")

            if not where_conditions:
                return {"success": False, "error": "No update criteria provided", "count": 0, "uuids": []}

            where_clause = " AND ".join(where_conditions)

            # Get UUIDs before update if requested
            uuids = []
            if return_uuids:
                select_query = f"SELECT uuid FROM order_reservations WHERE {where_clause}"
                cursor.execute(select_query, where_params)
                uuids = [row[0] for row in cursor.fetchall()]

            # Build SET clause
            set_conditions = []
            set_params = []

            if status is not None:
                set_conditions.append("status = %s")
                set_params.append(status)

            if new_section_id is not None:
                set_conditions.append("section_id = %s")
                set_params.append(str(new_section_id))

            set_conditions.append("last_update = NOW()")

            if not set_conditions:
                return {"success": False, "error": "No update values provided", "count": 0, "uuids": []}

            set_clause = ", ".join(set_conditions)

            # Execute update
            update_query = f"UPDATE order_reservations SET {set_clause} WHERE {where_clause}"
            cursor.execute(update_query, set_params + where_params)
            db.commit()
            updated_count = cursor.rowcount

            # Log the update
            criteria_desc = []
            if reservation_id is not None:
                criteria_desc.append(f"reservation_id={reservation_id}")
            if section_id is not None:
                criteria_desc.append(f"section_id={section_id}")
            if phone_number is not None:
                criteria_desc.append(f"phone={phone_number}")
            if order_number is not None:
                criteria_desc.append(f"order_number={order_number}")

            updates_desc = []
            if status is not None:
                updates_desc.append(f"status={status}")
            if new_section_id is not None:
                updates_desc.append(f"section_id={new_section_id}")

            self.logger.info(f"Updated {updated_count} reservations where {', '.join(criteria_desc)} with {', '.join(updates_desc)}")

            return {
                "success": True,
                "count": updated_count,
                "uuids": uuids
            }

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error updating reservations: {e}")
            return {"success": False, "error": str(e), "count": 0, "uuids": []}
        finally:
            cursor.close()
            db.close()
    
    def find_reservation_by_pin_and_status(self, insert_pin: str, status: int) -> Dict[str, Any]:
        """
        Find order reservation by reservation pin and status.
        Used for customer send orders (status=8 means ready for insert).
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT id, uuid, box_uuid, section_id, status, insert_pin, pickup_pin,
                       phone_number, size_category, expired, type, last_update, created_at
                FROM order_reservations
                WHERE insert_pin = %s AND status = %s
                ORDER BY created_at DESC
                LIMIT 1
            """
            cursor.execute(query, (insert_pin, status))
            result = cursor.fetchone()

            if result:
                return {
                    "success": True,
                    "reservation": result
                }
            else:
                return {
                    "success": False,
                    "error": "No reservation found with the given PIN and status"
                }
        except Exception as e:
            self.logger.error(f"Error finding reservation by PIN and status: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()



    def create_order_reservation(self, phone_number: str = None, section_id: int = None, status: int = None,
                                 lookup_pin: str = None, insert_pin: str = None, pickup_pin: str = None,
                                 auto_generate_pin: bool = True) -> Dict[str, Any]:
        """
        Unified method to create order reservations with flexible parameters.

        Args:
            phone_number: Phone number for the reservation (required unless lookup_pin is provided)
            section_id: Section ID for the reservation
            status: Status code for the reservation
            lookup_pin: PIN to lookup existing reservation for phone number (for customer_send operations)
            insert_pin: Specific insert PIN to use (overrides auto-generation)
            pickup_pin: Specific pickup PIN to use
            auto_generate_pin: Whether to auto-generate insert_pin if not provided

        Returns:
            Dict with success status, reservation details, and any errors
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # If lookup_pin is provided, find phone number from existing reservation
            if lookup_pin and not phone_number:
                cursor.execute("""
                    SELECT phone_number FROM order_reservations
                    WHERE insert_pin = %s OR pickup_pin = %s
                    ORDER BY created_at DESC LIMIT 1
                """, (lookup_pin, lookup_pin))

                existing_reservation = cursor.fetchone()
                if not existing_reservation:
                    return {"success": False, "error": "No reservation found with this PIN"}

                phone_number = existing_reservation['phone_number']

            # Validate required parameters
            if not phone_number or section_id is None or status is None:
                return {"success": False, "error": "Missing required parameters: phone_number, section_id, status"}

            # Generate unique UUID
            reservation_uuid = str(uuid4())

            # Handle insert_pin generation/assignment
            self.logger.info(f"create_order_reservation called with: phone_number={phone_number}, section_id={section_id}, status={status}, insert_pin='{insert_pin}', pickup_pin='{pickup_pin}', auto_generate_pin={auto_generate_pin}")

            # For reclaim operations (status=3), never auto-generate insert_pin
            if status == 3:
                self.logger.info(f"Reclaim operation detected (status=3), using provided insert_pin: '{insert_pin}' without auto-generation")
            elif auto_generate_pin and not insert_pin:
                from .pin_generator import generate_pin
                insert_pin = generate_pin()
                self.logger.info(f"Generated new insert_pin: '{insert_pin}'")
                if insert_pin is None:
                    return {"success": False, "error": "Failed to generate unique PIN"}
            else:
                self.logger.info(f"Using provided insert_pin: '{insert_pin}' (auto_generate_pin={auto_generate_pin})")

            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")

            # Determine type based on status
            type_mapping = {
                1: "delivered",
                2: "employee_send",
                3: "customer_reclaim",
                4: "customer_send"
            }
            reservation_type = type_mapping.get(status, "unknown")

            # Build query dynamically based on available fields
            fields = ["uuid", "box_uuid", "section_id", "status", "phone_number", "expired", "type", "created_at", "last_update"]
            values = [reservation_uuid, box_uuid, str(section_id), status, phone_number, 0, reservation_type, "NOW()", "NOW()"]
            placeholders = ["%s", "%s", "%s", "%s", "%s", "%s", "%s", "NOW()", "NOW()"]

            if insert_pin is not None:
                fields.insert(-3, "insert_pin")  # Insert before created_at, last_update, expired
                values.insert(-3, insert_pin)
                placeholders.insert(-3, "%s")

            if pickup_pin is not None:
                fields.insert(-3, "pickup_pin")  # Insert before created_at, last_update, expired
                values.insert(-3, pickup_pin)
                placeholders.insert(-3, "%s")

            query = f"""
                INSERT INTO order_reservations ({', '.join(fields)})
                VALUES ({', '.join(placeholders)})
            """

            # Remove NOW() from values for execution
            exec_values = [v for v in values if v != "NOW()"]
            cursor.execute(query, exec_values)
            db.commit()

            result = {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "section_id": section_id,
                "uuid": reservation_uuid,
                "phone_number": phone_number
            }

            if insert_pin:
                result["insert_pin"] = insert_pin
            if pickup_pin:
                result["pickup_pin"] = pickup_pin

            self.logger.info(f"Created order reservation: UUID={reservation_uuid}, phone={phone_number}, section={section_id}, status={status}, insert_pin={insert_pin}, pickup_pin={pickup_pin}")
            return result

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating order reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()


    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str):
        """Thuis function is called in pickup_process() after unlocking each seciton."""
        match endpoint_type:
            case "order/courier/pickup-expired":
                return self.update_reservation(section_id=section_id, status=0)     # deactivate order reservation
            case "order/courier/pickup":
                return self.update_reservation(section_id=section_id, status=0)     # deactivate order reservation
            case "order/customer/pickup":
                return self.update_reservation(section_id=section_id, status=0)     # deactivate order reservation
            case "order/employment/customer/pickup":
                return self.update_reservation(section_id=section_id, status=0)     # deactivate order reservation
            case _:
                pass  # No action needed for other endpoints





    async def create_reservation(
        self,
        order_uuid: str = None,     # if None, it will automaticky generate
        order_number: str = None,
        serial_number: str = None,
        status: int = 1,
        seciton_id: int = None,
        insert_pin: str = None,
        package_pin: str = None,
        pickup_pin: str = None,
        card_nubmer: str = None,
        age_control_required: bool = False,
        age_controlled: bool = 0,
        phone_nubmer: str = None,
        max_days: int = None,       # max days till it reservation expires
        payment_required: bool = False,
        price: float = 0,
        paid_status: bool = 0,      # if order was paid
        expired: int = 0,           # if order is expired
        type: str = None,           # type of order (magna, pradelna, etc.)
        section_can_change: int = 0     # if courier can change the section (0 = no, 1 = yes)
        ) -> Tuple[bool, Dict[str, Any]]:

        """
        Function to create new record in table order_reservations.

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple containing success (True/False) and reservation data
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate UUID if not provided
            if order_uuid is None:
                order_uuid = str(uuid4())

            # Generate PINs if not provided
            if insert_pin is None:
                from .pin_generator import generate_pin
                insert_pin = generate_pin()
                if insert_pin is None:
                    return False, {"error": "Failed to generate unique insert PIN"}

            if pickup_pin is None:
                from .pin_generator import generate_pin
                pickup_pin = generate_pin()
                if pickup_pin is None:
                    return False, {"error": "Failed to generate unique pickup PIN"}

            # Build the INSERT query
            query = """
                INSERT INTO order_reservations (
                    order_uuid, order_number, serial_number, status, section_id,
                    insert_pin, package_pin, pickup_pin, card_number,
                    age_control_required, age_controlled, phone_number, max_days,
                    price, payment_required, paid_status, expired, type,
                    sectionc_can_change, created_at, last_update
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, NOW(), NOW()
                )
            """

            # Execute the query
            cursor.execute(query, (
                order_uuid, order_number, serial_number, status, seciton_id,
                insert_pin, package_pin, pickup_pin, card_nubmer,
                age_control_required, age_controlled, phone_nubmer, max_days,
                price, payment_required, paid_status, expired, type,
                section_can_change
            ))
            db.commit()

            reservation_id = cursor.lastrowid

            # Return success with reservation data
            reservation_data = {
                "id": reservation_id,
                "order_uuid": order_uuid,
                "order_number": order_number,
                "serial_number": serial_number,
                "status": status,
                "section_id": seciton_id,
                "insert_pin": insert_pin,
                "package_pin": package_pin,
                "pickup_pin": pickup_pin,
                "card_number": card_nubmer,
                "age_control_required": age_control_required,
                "age_controlled": age_controlled,
                "phone_number": phone_nubmer,
                "max_days": max_days,
                "price": price,
                "payment_required": payment_required,
                "paid_status": paid_status,
                "expired": expired,
                "type": type,
                "section_can_change": section_can_change
            }

            self.logger.info(f"Created order reservation: ID={reservation_id}, UUID={order_uuid}, phone={phone_nubmer}, section={seciton_id}")
            return True, reservation_data

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating order reservation: {e}")
            return False, {"error": str(e)}
        finally:
            cursor.close()
            db.close()






    async def edit_reservaiton(
        self,
        order_uuid: str,        # Reservaiton uuid of reservation which I want to edit
        status: int = None,
        seciton_id: int = None,
        insert_pin: str = None,
        package_pin: str = None,
        pickup_pin: str = None,
        card_nubmer: str = None,
        age_control_required: bool = None,
        age_controlled: bool = None,
        phone_nubmer: str = None,
        ) -> Tuple[bool, Dict[str, Any]]:

        """
        Function to edit record in table order_reservations.

        Params:
            order_uuid: UUID of reservation which I want to edit
            all other: all other params are optional, if provided, they will be updated

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple containing success (True/False) and reservation data
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # First, check if the reservation exists
            cursor.execute("SELECT * FROM order_reservations WHERE order_uuid = %s", (order_uuid,))
            existing_reservation = cursor.fetchone()

            if not existing_reservation:
                return False, {"error": f"No reservation found with UUID: {order_uuid}"}

            # Build the UPDATE query dynamically based on provided parameters
            update_fields = []
            update_values = []

            if status is not None:
                update_fields.append("status = %s")
                update_values.append(status)

            if seciton_id is not None:
                update_fields.append("section_id = %s")
                update_values.append(seciton_id)

            if insert_pin is not None:
                update_fields.append("insert_pin = %s")
                update_values.append(insert_pin)

            if package_pin is not None:
                update_fields.append("package_pin = %s")
                update_values.append(package_pin)

            if pickup_pin is not None:
                update_fields.append("pickup_pin = %s")
                update_values.append(pickup_pin)

            if card_nubmer is not None:
                update_fields.append("card_number = %s")
                update_values.append(card_nubmer)

            if age_control_required is not None:
                update_fields.append("age_control_required = %s")
                update_values.append(age_control_required)

            if age_controlled is not None:
                update_fields.append("age_controlled = %s")
                update_values.append(age_controlled)

            if phone_nubmer is not None:
                update_fields.append("phone_number = %s")
                update_values.append(phone_nubmer)

            # Always update last_update timestamp
            update_fields.append("last_update = NOW()")

            if len(update_fields) == 1:  # Only last_update field
                return False, {"error": "No fields provided for update"}

            # Build and execute the UPDATE query
            query = f"UPDATE order_reservations SET {', '.join(update_fields)} WHERE order_uuid = %s"
            update_values.append(order_uuid)

            cursor.execute(query, update_values)
            db.commit()

            if cursor.rowcount == 0:
                return False, {"error": "No rows were updated"}

            # Fetch the updated reservation
            cursor.execute("SELECT * FROM order_reservations WHERE order_uuid = %s", (order_uuid,))
            updated_reservation = cursor.fetchone()

            self.logger.info(f"Updated order reservation: UUID={order_uuid}, updated_fields={len(update_fields)-1}")
            return True, {"reservation": updated_reservation}

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error editing order reservation: {e}")
            return False, {"error": str(e)}
        finally:
            cursor.close()
            db.close()








# Global repository instance
order_repository = OrderRepository()



