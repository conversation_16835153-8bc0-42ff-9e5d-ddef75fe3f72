import mysql.connector
import json
from os import getenv
from typing import List, Dict, Optional, Any

class SectionRepository:
    def list_sections(self) -> List[Dict]:
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service, lock_id, led_section, mode, type, size_width, size_depth, size_height, size_category
                FROM box_sections
                WHERE visible = 1
                ORDER BY section_id ASC
            """)
            sections = cursor.fetchall()

            # Add is_available field to each section
            for section in sections:
                section_id = section['section_id']
                identification_name = section['identification_name']
                section['is_available'] = self._check_section_availability(cursor, section_id, identification_name)

            return [dict(row) for row in sections]
        finally:
            cursor.close()
            conn.close()
            
    def get_section_data(self, section_id: str) -> Dict:
        """ Returns default data if section doesn't exist """
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service, lock_id, led_section, mode, type, visible
                FROM box_sections
                WHERE section_id = %s
            """, (section_id,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            else:
                # section doesn't exist, return default data
                return {
                    "section_id": section_id,
                    "identification_name": None,
                    "tempered": 0,
                    "blocked": 0,
                    "service": 0,
                    "lock_id": None,
                    "led_section": None,
                    "mode": None,
                    "type": None,
                    "visible": 0
                }
        finally:
            cursor.close()
            conn.close()

    def _check_section_availability(self, cursor, section_id: int, identification_name: str = None) -> bool:
        """Check if section is available (no active reservations)"""
        try:
            # Sections with identification_name == "stock" are always available
            if identification_name == "stock":
                return True

            # Check order_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM order_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            order_count = cursor.fetchone()['count']

            if order_count > 0:
                return False

            # Check sale_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM sale_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            sale_count = cursor.fetchone()['count']

            if sale_count > 0:
                return False

            # Check storage_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM storage_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            storage_count = cursor.fetchone()['count']

            return storage_count == 0

        except Exception as e:
            return False

    def get_box_layout(self) -> Optional[Dict]:
        """ returns layout from database based on last record """
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT box_layout
                FROM box_settings
                ORDER BY id DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            if result and result['box_layout']:
                try:
                    return json.loads(result['box_layout'])
                except json.JSONDecodeError as e:
                    print(f"JSON decode error in get_box_layout: {e}")
                    print(f"Invalid JSON data (first 200 chars): {result['box_layout'][:200]}")
                    print(f"Data around error position (char {e.pos}): {result['box_layout'][max(0, e.pos-50):e.pos+50]}")
                    print(f"Data at end (last 200 chars): {result['box_layout'][-200:]}")
                    print(f"Data type: {type(result['box_layout'])}")
                    print(f"Data length: {len(result['box_layout'])}")
                    
                    try:
                        decoder = json.JSONDecoder()
                        decoder.decode(result['box_layout'])
                    except json.JSONDecodeError as e2:
                        print(f"Valid JSON ends at position: {e2.pos}")
                        print(f"Extra data after valid JSON: {result['box_layout'][e2.pos:]}")
                        
                        try:
                            valid_json = result['box_layout'][:e2.pos]
                            print(f"Attempting to parse truncated JSON...")
                            return json.loads(valid_json)
                        except json.JSONDecodeError as e3:
                            print(f"Even truncated JSON is invalid: {e3}")
                            return None
                    
                    return None
            return None
        finally:
            cursor.close()
            conn.close()

    def get_available_sections(self, mode: str = "any"):
        """ returns list of available sections based on mode """
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            if mode == "any":
                cursor.execute("""
                    SELECT section_id
                    FROM box_sections
                    WHERE visible = 1
                    ORDER BY section_id ASC
                """)
            else:
                cursor.execute("""
                    SELECT section_id
                    FROM box_sections
                    WHERE visible = 1 AND mode = %s
                    ORDER BY section_id ASC
                """, (mode,))
            return [row['section_id'] for row in cursor.fetchall()]
        finally:
            cursor.close()
            conn.close()



    def save_to_orders_created(self,
        order_number: str,
        order_uuid: str,
        box_lockers: List[int],
        box_locker_packages: List[Dict[str, Any]]
        
        ) -> None:
        pass


section_repository = SectionRepository()
