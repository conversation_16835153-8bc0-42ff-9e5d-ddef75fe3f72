"""
Consolidated Payment Manager

This module consolidates all payment-related functionality that was previously split across:
- managers/transaction_logger.py (transaction logging functions)
- managers/transaction_handler.py (WebSocket transaction handling)
- managers/payment_manager.py (payment processing and callbacks)

Features:
- Database transaction logging for sale and storage operations
- Universal payment processing with WebSocket integration
- Payment callback handling and message queue management
- WebSocket transaction handling for payment flows
- Database connection management for payment operations

All payment-related code is now centralized in this single module for better maintainability.
"""

import asyncio
import logging
import httpx
import mysql.connector
import json
from datetime import datetime
from typing import Dict, Optional
from fastapi import WebSocket, WebSocketDisconnect
from os import getenv
from dotenv import load_dotenv

from config import device_config
from managers import ws_manager
from managers.session_manager import session_manager
from transaction.models import TransactionState, TransactionContext
from transaction.manager import transaction_manager

load_dotenv()

logger = logging.getLogger(__name__)

# Global registry for active message queues (for universal payment callback injection)
_active_message_queues: Dict[str, asyncio.Queue] = {}


def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )


def log_sale_transaction(
    uuid: str,
    result: str,
    request: Optional[dict] = None,
    response: Optional[dict] = None,
    transaction_type: str = "sale",
    msg: Optional[str] = None
):
    """
    Logs a sale transaction to the sale_transactions table.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()

    try:
        query = """
            INSERT INTO sale_transactions (
                uuid, type, msg, result, request, response
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        values = (
            uuid,
            transaction_type,
            msg,
            result,
            json.dumps(request) if request else None,
            json.dumps(response) if response else None
        )
        cursor.execute(query, values)
        conn.commit()
        logger.info(f"Logged sale transaction: {uuid} - {result}")
        return cursor.lastrowid
    except mysql.connector.Error as err:
        conn.rollback()
        logger.error(f"Database error logging sale transaction: {err}")
        return None
    finally:
        cursor.close()
        conn.close()


def log_storage_transaction(
    uuid: str,
    result: str,
    request: Optional[dict] = None,
    response: Optional[dict] = None,
    transaction_type: str = "storage",
    msg: Optional[str] = None
):
    """
    Logs a storage transaction to the storage_transactions table.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()

    try:
        query = """
            INSERT INTO storage_transactions (
                uuid, type, msg, result, request, response
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        values = (
            uuid,
            transaction_type,
            msg,
            result,
            json.dumps(request) if request else None,
            json.dumps(response) if response else None
        )
        cursor.execute(query, values)
        conn.commit()
        logger.info(f"Logged storage transaction: {uuid} - {result}")
        return cursor.lastrowid
    except mysql.connector.Error as err:
        conn.rollback()
        logger.error(f"Database error logging storage transaction: {err}")
        return None
    finally:
        cursor.close()
        conn.close()


def register_message_queue(session_id: str, message_queue: asyncio.Queue) -> None:
    """
    Register a message queue for payment callback injection.
    Used by all modules (storage, product, order) that need payment callbacks.
    
    Args:
        session_id: Session ID to register queue for
        message_queue: The asyncio.Queue to register
    """
    _active_message_queues[session_id] = message_queue
    logger.info(f"Registered message queue for payment callbacks: {session_id}")


def unregister_message_queue(session_id: str) -> None:
    """
    Unregister a message queue for payment callback injection.
    Should be called when a session ends.
    
    Args:
        session_id: Session ID to unregister queue for
    """
    if session_id in _active_message_queues:
        _active_message_queues.pop(session_id)
        logger.info(f"Unregistered message queue for payment callbacks: {session_id}")


async def inject_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """
    Universal payment callback injection function.
    Injects payment status directly into the registered message queue for any module.
    
    Args:
        session_id: Session ID to inject callback for
        status: Payment status ("success" or "failed")
        message: Optional message from payment service
        
    Returns:
        bool: True if injection successful, False otherwise
    """
    logger.info(f"Universal payment callback injection for session {session_id}: {status}")
    
    try:
        # Check if we have an active message queue for this session
        if session_id not in _active_message_queues:
            logger.error(f"No active message queue found for payment callback: {session_id}")
            return False
            
        message_queue = _active_message_queues[session_id]
        
        # Create payment status message
        success = status == "success"
        callback_message = {
            "type": "payment_status",
            "success": success,
            "message": message or ("Payment successful" if success else "Payment failed")
        }
        
        # Inject the callback message into the queue
        await message_queue.put(callback_message)
        logger.info(f"Payment callback injected successfully for session {session_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error injecting payment callback for session {session_id}: {e}")
        return False


async def handle_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """
    Universal payment callback handler for all modules.
    Simply calls inject_payment_callback to inject the callback into the message queue.
    
    Args:
        session_id: Session ID to handle callback for
        status: Payment status ("success" or "failed")
        message: Optional message from payment service
        
    Returns:
        bool: True if callback handled successfully, False otherwise
    """
    logger.info(f"Universal payment callback handler for session {session_id}: {status}")
    return await inject_payment_callback(session_id, status, message)


async def payment_process_simple(amount: float) -> bool:
    """
    Universal payment process function.
    Simple function that processes payment and returns success/failure.
    
    Args:
        amount: Payment amount in currency units
        
    Returns:
        bool: True if payment successful, False if failed
    """
    logger.info(f"Starting universal payment process for amount: {amount}")
    
    if amount <= 0:
        logger.info("No payment needed (amount <= 0)")
        return True
    
    try:
        # Prepare payment data (same format as existing code)
        payment_data = {
            "type": "sale",
            "amount": float(amount),
            "variable_symbol": f"payment_{int(datetime.now().timestamp())}"  # Unique identifier
        }
        
        logger.info(f"Processing payment: {payment_data}")
        
        # Get payment service configuration
        payment_service_timeout = device_config.payment_config.get("payment_service_timeout", 30)
        payment_service_url = device_config.payment_config.get("payment_service_url")
        
        if not payment_service_url:
            logger.error("Payment service URL not configured")
            return False
        
        # Send payment request to payment service
        async with httpx.AsyncClient(timeout=payment_service_timeout) as client:
            response = await client.post(
                payment_service_url,
                json=payment_data
            )
            
            if response.status_code == 200:
                logger.info(f"Payment successful: {response.status_code} - {response.text}")
                return True
            else:
                logger.error(f"Payment failed: {response.status_code} - {response.text}")
                return False
                
    except httpx.TimeoutException:
        logger.error(f"Payment service timeout for amount {amount}")
        return False
        
    except Exception as e:
        logger.error(f"Payment error: {e}")
        return False


async def payment_process_with_callbacks(session_id: str, amount: float, message_queue: asyncio.Queue) -> bool:
    """
    Full payment process with WebSocket messages and callback handling.
    Used when payment needs to be integrated with WebSocket flow.
    
    Args:
        session_id: WebSocket session ID
        amount: Payment amount in currency units
        message_queue: Queue to receive WebSocket messages
        
    Returns:
        bool: True if payment successful, False if failed
    """
    logger.info("Payment required, starting payment process")

    # Register message queue for universal payment callback injection
    register_message_queue(session_id, message_queue)

    try:
        # Send start payment screen message
        await ws_manager.send(session_id, {
            "type": "start_payment_screen",
            "wait_for_ready": True
        })

        # Wait for payment_screen_ready
        while True:
            try:
                message = await message_queue.get()
            except Exception as e:
                logger.error(f"Error waiting for payment message: {e}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing payment message: {message_type}")

            if message_type == "payment_screen_ready":
                logger.info("Payment screen ready - starting payment terminal call")

                # Send payment initiation status
                await ws_manager.send(session_id, {
                    "type": "payment_status",
                    "status": "initiating"
                })

                # Call payment terminal (this initiates payment but doesn't wait for result)
                payment_initiated = await payment_process_simple(amount)

                if payment_initiated:
                    # Send payment processing status
                    await ws_manager.send(session_id, {
                        "type": "payment_status",
                        "status": "processing"
                    })
                    logger.info("Payment initiated successfully, waiting for callback")
                    # Continue waiting for payment_status callback - don't break here
                else:
                    # Payment terminal call failed
                    await ws_manager.send(session_id, {
                        "type": "payment_result",
                        "success": False,
                        "message": "Payment failed to initiate"
                    })
                    return False

            elif message_type == "payment_status":
                # Handle payment status from payment callback
                logger.info("Received payment status callback")

                success = message.get("success", False)

                # Send payment result
                await ws_manager.send(session_id, {
                    "type": "payment_result",
                    "success": success,
                    "message": "Payment successful" if success else "Payment failed"
                })

                if success:
                    logger.info("Payment completed successfully")
                    return True
                else:
                    logger.info("Payment failed")
                    return False

            elif message_type == "storno":
                logger.info("Payment cancelled by user")
                return False

    except Exception as e:
        logger.error(f"Error in payment_process: {e}")
        return False
    finally:
        # Clean up universal message queue registration
        unregister_message_queue(session_id)

async def handle_transaction_websocket(
    websocket: WebSocket,
    session_id: str,
    context: Optional[TransactionContext] = None
):
    """Handle WebSocket communication for transactions"""

    logger.info(f"Transaction WebSocket connected: {session_id}")

    # Get or validate context
    try:
        if not context:
            context = transaction_manager.active_transactions.get(session_id)
            logger.info(f"Retrieved transaction context: {context}")

            if not context:
                logger.error(f"No active transaction found for session: {session_id}")
                await websocket.close(code=4000, reason="No active transaction")
                return
    except Exception as e:
        logger.error(f"Error getting transaction context: {e}")
        await websocket.close(code=4000, reason="Error getting transaction context")
        return

    try:
        # Register connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for session: {session_id}")

        # Send initial state
        initial_state = {
            "type": "transaction_state",
            "state": context.current_state.value,
            "data": {
                "payment_data": context.payment_data,
                "hardware_data": context.hardware_data,
                "metadata": context.metadata
            },
            "message": "Připojeno k transakci"
        }
        logger.info(f"Sending initial state: {initial_state}")

        await ws_manager.send(session_id, initial_state)

        # Start payment processing if in PAYMENT_PENDING state
        if context.current_state == TransactionState.PAYMENT_PENDING:
            logger.info(f"Starting payment processing for session {session_id}")
            try:
                success = await transaction_manager.start_payment_processing(session_id)

                if not success:
                    logger.error(f"Failed to start payment processing for session {session_id}")
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Nepodařilo se zahájit platbu"
                    })
                    return

                logger.info(f"Payment processing started successfully for session {session_id}")

            except Exception as e:
                logger.error(f"Error starting payment processing: {e}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": f"Error starting payment: {str(e)}"
                })
                return

        # Main message loop
        await _handle_transaction_message_loop(websocket, session_id, context)

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Unexpected error in WebSocket handler: {e}")
        try:
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Neočekávaná chyba: {str(e)}"
            })
        except:
            pass
    finally:
        # Cleanup
        ws_manager.disconnect(session_id)
        logger.info(f"WebSocket connection closed: {session_id}")


async def _handle_transaction_message_loop(websocket: WebSocket, session_id: str, context: TransactionContext):
    """Handle the main message loop for transaction WebSocket"""
    while ws_manager.is_connected(session_id):
        try:
            message = await websocket.receive_text()
            logger.debug(f"Received message: {message}")

            data = json.loads(message)
            msg_type = data.get("type")

            logger.info(f"Processing message type: {msg_type}")

            if msg_type == "ping":
                await ws_manager.send(session_id, {
                    "type": "pong",
                    "message": "Connection alive"
                })
                continue

            # Handle state-specific messages
            if context.current_state == TransactionState.HARDWARE_READY:
                if msg_type == "confirm_pickup":
                    logger.info(f"Processing hardware pickup confirmation for session {session_id}")
                    logger.info(f"Current context state: {context.current_state}")
                    logger.info(f"Payment data: {context.payment_data}")

                    # Open the locker
                    success = await transaction_manager.open_locker_for_pickup(session_id)
                    if not success:
                        logger.error(f"Failed to open locker for session {session_id}")
                        await ws_manager.send(session_id, {
                            "type": "hardware_status",
                            "status": "error",
                            "message": "Nepodařilo se otevřít schránku"
                        })
                        break

                    logger.info(f"Locker opening initiated for session {session_id}")

            elif context.current_state == TransactionState.PAYMENT_PROCESSING:
                if msg_type == "cancel_payment":
                    logger.info("Processing payment cancellation")
                    # Handle payment cancellation
                    context.current_state = TransactionState.ERROR
                    context.add_event("payment_cancelled")

                    await ws_manager.send(session_id, {
                        "type": "payment_status",
                        "status": "cancelled",
                        "message": "Platba byla zrušena"
                    })
                    break
            else:
                logger.info(f"Received message type '{msg_type}' in state '{context.current_state}' - no handler defined")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid message format: {e}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Neplatný formát zprávy"
            })
            continue
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected: {session_id}")
            break
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Chyba při zpracování zprávy: {str(e)}"
            })
