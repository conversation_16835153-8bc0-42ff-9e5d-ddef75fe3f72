import mysql.connector
from typing import Optional
from os import getenv
from dotenv import load_dotenv
import logging
import asyncio
from datetime import datetime

load_dotenv()

logger = logging.getLogger(__name__)

# Import jetveo_client for journal API calls
from infrastructure.external_apis.jetveo_client import jetveo_client

def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

def log_timeline_event(
    serial_number: Optional[str] = getenv("SERIAL_NUMBER"),
    entered_pin: Optional[str] = None,
    event_type: Optional[str] = None,
    event_result: Optional[str] = None,
    operator_id: Optional[int] = None,
    section_id: Optional[str] = None,
    tempered_unlock: Optional[int] = None,
    box_status: Optional[str] = None,
    message: Optional[str] = None,
    mode: Optional[str] = None,
    session_id: Optional[str] = None,
    order_number: Optional[str] = None,
    type: Optional[str] = None,
    phone_number: Optional[str] = None
):
    """
    Logs an event to the timeline_log table and sends data to journal API.
    """
    conn = _get_db_connection()
    cursor = conn.cursor()

    try:
        query = """
            INSERT INTO timeline_log (
                serial_number, entered_pin, event_type, event_result, operator_id,
                section_id, tempered_unlock, box_status, message, mode, session_id
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        values = (
            serial_number, entered_pin, event_type, event_result, operator_id,
            section_id, tempered_unlock, box_status, message, mode, session_id
        )
        cursor.execute(query, values)
        conn.commit()
        logger.info(f"Logged event: {event_type} for session {session_id}")

        # Send data to journal API asynchronously
        _send_to_journal_async(
            serial_number=serial_number,
            entered_pin=entered_pin,
            event_type=event_type,
            event_result=event_result,
            operator_id=str(operator_id) if operator_id is not None else None,
            section_id=str(section_id) if section_id is not None else None,
            tempered_unlock=str(tempered_unlock) if tempered_unlock is not None else None,
            message=message,
            session_id=session_id,
            order_number=order_number,
            type=type
        )

        return cursor.lastrowid
    except mysql.connector.Error as err:
        conn.rollback()
        logger.error(f"Database error logging timeline event: {err}")
        return None
    finally:
        cursor.close()
        conn.close()


def _send_to_journal_async(
    serial_number: Optional[str],
    entered_pin: Optional[str] = None,
    event_type: Optional[str] = None,
    event_result: Optional[str] = None,
    operator_id: Optional[str] = None,
    section_id: Optional[str] = None,
    tempered_unlock: Optional[str] = None,
    message: Optional[str] = None,
    session_id: Optional[str] = None,
    order_number: Optional[str] = None,
    type: Optional[str] = None
):
    """
    Helper function to send journal data asynchronously without blocking the main thread.
    """
    if not serial_number:
        logger.debug("No serial number provided, skipping journal API call")
        return

    # Generate timestamp in the required format: "2025-08-17T14:59:00.000Z"
    timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

    # Create a task to send journal data without blocking
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're in an async context, create a task
            loop.create_task(_send_journal_data_task(
                serial_number, timestamp, entered_pin, event_result, event_type,
                message, operator_id, order_number, section_id, session_id,
                tempered_unlock, type
            ))
        else:
            # If no loop is running, run in a new thread
            import threading
            thread = threading.Thread(
                target=_run_journal_send,
                args=(serial_number, timestamp, entered_pin, event_result, event_type,
                      message, operator_id, order_number, section_id, session_id,
                      tempered_unlock, type),
                daemon=True
            )
            thread.start()
    except RuntimeError:
        # No event loop exists, run in a new thread
        import threading
        thread = threading.Thread(
            target=_run_journal_send,
            args=(serial_number, timestamp, entered_pin, event_result, event_type,
                  message, operator_id, order_number, section_id, session_id,
                  tempered_unlock, type),
            daemon=True
        )
        thread.start()


async def _send_journal_data_task(
    serial_number: str,
    timestamp: str,
    entered_pin: Optional[str] = None,
    event_result: Optional[str] = None,
    event_type: Optional[str] = None,
    message: Optional[str] = None,
    operator_id: Optional[str] = None,
    order_number: Optional[str] = None,
    section_id: Optional[str] = None,
    session_id: Optional[str] = None,
    tempered_unlock: Optional[str] = None,
    type: Optional[str] = None
):
    """
    Async task to send journal data to the API.
    """
    try:
        success = await jetveo_client.send_to_journal(
            serial_number=serial_number,
            timestamp=timestamp,
            entered_pin=entered_pin,
            event_result=event_result,
            event_type=event_type,
            message=message,
            operator_id=operator_id,
            order_number=order_number,
            section_id=section_id,
            session_id=session_id,
            tempered_unlock=tempered_unlock,
            type=type
        )
        if not success:
            logger.warning(f"Failed to send journal data for event: {event_type}")
    except Exception as e:
        logger.error(f"Error sending journal data: {e}")


def _run_journal_send(
    serial_number: str,
    timestamp: str,
    entered_pin: Optional[str] = None,
    event_result: Optional[str] = None,
    event_type: Optional[str] = None,
    message: Optional[str] = None,
    operator_id: Optional[str] = None,
    order_number: Optional[str] = None,
    section_id: Optional[str] = None,
    session_id: Optional[str] = None,
    tempered_unlock: Optional[str] = None,
    type: Optional[str] = None
):
    """
    Run journal data send in a new event loop (for thread execution).
    """
    asyncio.run(_send_journal_data_task(
        serial_number, timestamp, entered_pin, event_result, event_type,
        message, operator_id, order_number, section_id, session_id,
        tempered_unlock, type
    ))
