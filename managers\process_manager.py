"""
Universal Process Manager.
Contains universal functions used by storage, order, and product modules:
- pickup_process: Universal pickup process with payment support
- payment_process: Universal payment process with storno support  
- select_sections: Universal section selection process
"""

import logging
import asyncio
from typing import List, Tuple, Any

from managers.ws_manager import ws_manager
from managers.session_manager import session_manager

logger = logging.getLogger(__name__)
from managers.sequence_manager import SequenceManager
from managers.session_manager import SectionConfig
from managers.payment_manager import (
    register_message_queue,
    unregister_message_queue,
    payment_process_with_callbacks
)


async def _start_section_sequence(session_id: str, section_ids: List[int], wait_for_completion: bool = False) -> Tuple[bool, List[int]]:
    """
    Helper function to create SectionConfig objects and start FSM sequence.

    Args:
        session_id: WebSocket session ID
        section_ids: List of section IDs to open
        wait_for_completion: Whether to wait for sequence completion or return immediately

    Returns:
        Tuple of (success, successful_sections)
    """
    from managers.sequence_manager import SequenceManager
    from managers.session_manager import SectionConfig

    sequence_manager = SequenceManager()

    # Send acknowledgment that hardware screen ready was received
    await ws_manager.send(session_id, {
        "type": "hardware_status",
        "status": "preparing",
        "message": f"Připravuji schránky"
    })

    # Convert section IDs to SectionConfig objects
    section_configs = []
    for section_id in section_ids:
        section_configs.append(SectionConfig(
            section_id=section_id,
            lock_id=section_id,  # Default to same as section_id
            is_tempered=True,  # Default to tempered for pickup operations
            led_section=section_id  # Set LED section to same as section_id
        ))

    # Start FSM sequence
    sequence_started = await sequence_manager.start_fsm_sequence(
        session_id=session_id,
        sections=section_configs,
        pin=None
    )

    successful_sections = []

    if sequence_started:
        if wait_for_completion:
            # Wait for sequence completion pattern
            logger.info(f"FSM sequence started successfully, waiting for completion")

            # Wait for the FSM sequence to complete by waiting for the task
            if session_id in sequence_manager.active_sequences:
                try:
                    # Wait for the sequence task to complete
                    await sequence_manager.active_sequences[session_id]
                    logger.info(f"FSM sequence completed successfully for {len(section_ids)} sections")
                    successful_sections.extend(section_ids)
                except Exception as e:
                    logger.error(f"FSM sequence failed with error: {e}")
                    return False, []
            else:
                logger.error("FSM sequence task not found in active sequences")
                return False, []
        else:
            # Immediate success pattern - add all sections to successful list
            successful_sections.extend(section_ids)
    else:
        logger.error("Failed to start FSM sequence")
        return False, []

    return True, successful_sections


# Payment functions moved to managers/payment_manager.py
sequence_manager = SequenceManager()






async def pickup_process(sections: List[int], session_id: str, message_queue: asyncio.Queue, requires_payment: bool = False) -> Tuple[bool, List[int]]:
    """
    Universal pickup process function used by storage, order, and product modules.

    This function handles ALL pickup-related WebSocket messages:
    - payment_screen_ready: processes payment if required
    - hardware_screen_ready: starts FSM sequence for all sections, then ends pickup process
    - open_sections: starts FSM sequence for multiple sections (expects list of integers)
    - hardware_screen_stop: ends pickup process

    The pickup process also ends when WebSocket connection is closed.

    Args:
        sections: List of section IDs to pickup
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        requires_payment: Whether payment is required before pickup

    Returns:
        Tuple of (success, successful_sections)
    """
    logger.info(f"Starting pickup_process for {len(sections)} sections: {sections}")
    
    picking_up = True
    successful_sections = []

    # Register message queue for universal payment callback injection
    logger.info(f"Registering message queue for payment callbacks with session_id: {session_id}")
    register_message_queue(session_id, message_queue)

    # Import sequence manager for FSM operations

    try:
        # Handle payment if required using payment_process_with_callbacks
        if requires_payment:
            # Get amount from session
            session = session_manager.get_session(session_id)
            amount = getattr(session, 'amount', 0) if session else 0

            payment_success = await payment_process_with_callbacks(
                session_id=session_id,
                amount=amount,
                message_queue=message_queue
            )
            if not payment_success:
                logger.info("Payment failed or cancelled - ending pickup process")
                return False, []
            
        # Only send start_hardware_screen if we have sections to open
        if sections:
            await ws_manager.send(session_id, {
                "type": "start_hardware_screen",
                "wait_for_ready": True
            })
        else:
            # No sections to open, payment-only mode - return success immediately
            logger.info("Payment-only mode completed successfully (no sections to open)")
            return True, []
        
        # Main pickup loop - handle all message types
        while picking_up:
            # Check if WebSocket connection is still active
            if not ws_manager.is_connected(session_id):
                logger.info(f"WebSocket disconnected - ending pickup process for session {session_id}")
                # Clean up session when WebSocket disconnects
                await session_manager.remove_session(session_id)
                return True, successful_sections

            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in pickup_process: {e}")
                # End pickup process when WebSocket disconnects and clean up session
                logger.info(f"Ending pickup process due to WebSocket disconnection for session {session_id}")
                await session_manager.remove_session(session_id)
                return True, successful_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(sections)} sections")

                # Use helper function to start section sequence with completion waiting
                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=sections,
                    wait_for_completion=True
                )

                if sequence_success:
                    successful_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")


            elif message_type == "open_sections":
                section_ids = message.get("section_ids")

                # Validate that section_ids is a list of integers
                if not isinstance(section_ids, list):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "section_ids must be a list"
                    })
                    continue

                if not all(isinstance(sid, int) for sid in section_ids):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "All section_ids must be integers"
                    })
                    continue

                # Check if all section IDs are valid
                invalid_sections = [sid for sid in section_ids if sid not in sections]
                if invalid_sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": f"Invalid sections: {invalid_sections}"
                    })
                    continue

                # Use helper function to start section sequence
                logger.info(f"Opening multiple sections: {section_ids}")

                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=section_ids
                )

                if sequence_success:
                    successful_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

            
            elif message_type == "hardware_screen_stop":
                # Stop pickup sequence or end pickup process
                logger.info(f"Received hardware_screen_stop command - ending pickup process for session {session_id}")
                picking_up = False

                await ws_manager.send(session_id, {
                    "type": "hardware_screen_completed",
                    "message": "Pickup process completed",
                    "successful_sections": successful_sections
                })

                # Delete websocket session and clean up
                logger.info(f"Cleaning up WebSocket session for {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)

                return True, successful_sections

        return True, successful_sections
        
    except Exception as e:
        logger.error(f"Error in pickup_process: {e}")
        return False, []
    finally:
        # Clean up universal message queue registration
        unregister_message_queue(session_id)


# Payment callback functions moved to managers/payment_manager.py


async def select_sections(reserved_sections: List[int], available_sections: List[int], session_id: str, message_queue: asyncio.Queue) -> Tuple[bool, List[int]]:
    """
    Universal section selection function used by order and other modules.
    Works exactly like pickup_process() but for section selection.

    Args:
        reserved_sections: List of reserved section IDs (usually all available)
        available_sections: List of available section IDs to select from
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        wait_for_stop: Whether to wait for stop_selection command (default: True)

    Returns:
        Tuple of (success, selected_sections)
    """
    logger.info(f"Starting section selection for session {session_id} with {len(available_sections)} available sections: {available_sections}")

    selecting = True
    successful_sections = []
    print(f"Reserved sections: {reserved_sections}")

    # Register message queue for universal payment callback injection
    register_message_queue(session_id, message_queue)

    # Import sequence manager for FSM operations
    try:
        # No payment required, go directly to hardware screen
        await ws_manager.send(session_id, {
            "type": "start_hardware_screen",
            "wait_for_ready": True
        })
    
        # Main selection loop - handle all message types
        while selecting:
            # Check if WebSocket connection is still active
            if not ws_manager.is_connected(session_id):
                logger.info(f"WebSocket disconnected - ending section selection for session {session_id}")
                # Clean up session when WebSocket disconnects
                await session_manager.remove_session(session_id)
                return True, successful_sections

            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in select_sections: {e}")
                # End selection process when WebSocket disconnects and clean up session
                logger.info(f"Ending section selection due to WebSocket disconnection for session {session_id}")
                await session_manager.remove_session(session_id)
                return True, successful_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(reserved_sections)} sections")

                # Use helper function to start section sequence with completion waiting
                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=reserved_sections,
                    wait_for_completion=True
                )

                if sequence_success:
                    successful_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

            elif message_type == "open_sections":
                section_ids = message.get("section_ids")

                # Validate that section_ids is a list of integers
                if not isinstance(section_ids, list):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "section_ids must be a list"
                    })
                    continue

                if not all(isinstance(sid, int) for sid in section_ids):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "All section_ids must be integers"
                    })
                    continue

                # Check if all section IDs are valid
                invalid_sections = [sid for sid in section_ids if sid not in available_sections]
                if invalid_sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": f"Invalid sections: {invalid_sections}"
                    })
                    continue

                # Use helper function to start section sequence
                logger.info(f"Opening multiple sections: {section_ids}")

                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=section_ids
                )

                if sequence_success:
                    successful_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")


            elif message_type == "hardware_screen_stop":
                # Stop selection sequence or end selection process
                logger.info(f"Received hardware_screen_stop command - ending selection process for session {session_id}")
                selecting = False

                await ws_manager.send(session_id, {
                    "type": "hardware_screen_completed",
                    "message": "Selection completed",
                    "selected_sections": successful_sections
                })

                # Delete websocket session and clean up
                logger.info(f"Cleaning up WebSocket session for {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)


                return True, successful_sections

        return True, successful_sections

    except Exception as e:
        logger.error(f"Error in select_sections: {e}")
        return False, []
    finally:
        # Clean up universal message queue registration
        unregister_message_queue(session_id)






async def age_check_process(session_id: str, message_queue: asyncio.Queue) -> bool:
    """
    Function to check customer's age at pickup_process

    Returns:
        bool: True if customer is over 18, False otherwise
    """

    # TODO: implement this funciton later, when i will now how it will wokrk

    return True



