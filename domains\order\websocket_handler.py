"""
WebSocket handler for order operations.
Uses universal pickup_process() and select_sections() functions for all operations.
"""

import json
import logging
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
# OrderRepository import removed - reservations now created via /order/create_reservation endpoint
from infrastructure.repositories.section_repository import section_repository

logger = logging.getLogger(__name__)


async def handle_order_websocket(websocket: WebSocket, session_id: str):
    """
    Universal WebSocket handler for all order operations.
    Routes to pickup_process() or select_sections() based on operation type.
    """
    logger.info(f"Order WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Order WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        # Get operation details from session
        operation = getattr(session, 'operation', 'unknown')
        logger.info(f"Order operation: {operation}")

        # Route to appropriate handler based on operation type
        if operation in ["pickup_expired", "pickup_employee", "customer_pickup"]:
            await _handle_pickup_operation(websocket, session_id, session, operation)
        elif operation in ["deliver_employee", "employee_send", "customer_reclaim", "customer_send", "deliver_order"]:
            await _handle_selection_operation(websocket, session_id, session, operation)
        else:
            logger.error(f"Unknown operation: {operation}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Unknown operation: {operation}"
            })

    except Exception as e:
        logger.error(f"Error in order WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Order WebSocket handler ended for session: {session_id}")


async def _handle_pickup_operation(websocket: WebSocket, session_id: str, session, operation: str):
    """Handle pickup operations using pickup_process()"""
    logger.info(f"Handling pickup operation: {operation}")

    # Get sections and operation details from session
    user_data = session.user_data or {}
    sections = user_data.get('sections', [])
    section_id = getattr(session, 'section_id', None)  # This field exists in SessionData
    reservation_id = user_data.get('reservation_id', None)

    # For single section operations, convert to list
    if section_id and not sections:
        sections = [section_id]

    if not sections:
        logger.error(f"No sections found in session {session_id}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": "No sections found"
        })
        return

    # Create message queue for pickup_process
    message_queue = asyncio.Queue()

    # Message handler to route WebSocket messages to pickup_process
    async def handle_websocket_messages():
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue

                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue

                msg_type = data.get("type")

                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue

                # Route message to pickup_process
                await message_queue.put(data)

            except WebSocketDisconnect:
                logger.info(f"Order pickup WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                break

    # Start message handler task
    message_task = asyncio.create_task(handle_websocket_messages())

    # Start pickup_process directly from universal process_manager
    from managers.process_manager import pickup_process
    success, successful_sections = await pickup_process(
        sections=sections,
        session_id=session_id,
        message_queue=message_queue,
        requires_payment=False  # Orders don't require payment
    )

    # Cancel message handler
    message_task.cancel()


    logger.info(f"Order pickup completed for session {session_id}: success={success}")


async def _handle_selection_operation(websocket: WebSocket, session_id: str, session, operation: str):
    """Handle selection operations using select_sections()"""
    logger.info(f"Handling selection operation: {operation}")

    # Get operation details from session
    user_data = session.user_data or {}
    phone_number = user_data.get('phone_number', None)
    reservation_pin = user_data.get('reservation_pin', None)
    reserved_section_id = user_data.get('reserved_section_id', None)
    reserved_section_ids = user_data.get('reserved_section_ids', [])
    available_sections = section_repository.get_available_sections("order")

    logger.info(f"Operation: {operation}, reserved_section_id: {reserved_section_id}, reserved_section_ids: {reserved_section_ids}")

    # Determine available sections based on operation
    if operation == "deliver_employee" and reserved_section_ids:
        # Courier delivering to employee - use reserved sections
        available_sections = reserved_section_ids
        reserved_sections = reserved_section_ids
    elif operation in ["employee_send", "customer_reclaim", "customer_send"] and reserved_section_id:
        # Single section operations - use reserved section
        available_sections = [reserved_section_id]
        reserved_sections = [reserved_section_id]
    elif operation in ["employee_send", "customer_reclaim", "customer_send"] and reserved_section_ids:
        # Multiple section operations - use reserved sections
        available_sections = reserved_section_ids
        reserved_sections = reserved_section_ids
    else:
        # Get all available sections (fallback)
        available_sections = section_repository.get_available_sections("order")
        reserved_sections = available_sections

    logger.info(f"Final sections - available: {available_sections}, reserved: {reserved_sections}")

    # Create message queue for select_sections
    message_queue = asyncio.Queue()

    # Message handler to route WebSocket messages to select_sections
    async def handle_websocket_messages():
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue

                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue

                msg_type = data.get("type")

                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue

                # Route message to select_sections
                await message_queue.put(data)

            except WebSocketDisconnect:
                logger.info(f"Order selection WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                break

    # Start message handler task
    message_task = asyncio.create_task(handle_websocket_messages())

    # Start select_sections from universal process_manager
    from managers.process_manager import select_sections
    success, successful_sections = await select_sections(
        reserved_sections=reserved_sections,
        available_sections=available_sections,
        session_id=session_id,
        message_queue=message_queue
    )

    # Cancel message handler
    message_task.cancel()

    # Order selection completed - reservations will be created via /order/create_reservation endpoint
    # The frontend should call /order/create_reservation with session_id and successful_sections

    logger.info(f"Order selection completed for session {session_id}: success={success}")

# Old flow-based handler removed - now using universal pickup_process() and select_sections()
