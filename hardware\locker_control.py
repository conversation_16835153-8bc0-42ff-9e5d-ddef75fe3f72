"""
High-level hardware control interface for lockers and LED system.
Provides clean abstraction over electronics_api for common operations.
"""

import asyncio
import logging
import mysql.connector
from os import getenv
from typing import Dict, List, Optional
from .electronics_api import send_command
from config import device_config
from managers.timeline_logger import log_timeline_event

logger = logging.getLogger(__name__)

class LEDColor:
    """LED color constants"""
    NONE = None  # Special value for turning off LED
    YELLOW = device_config.fsm_config["led_colors"]["yellow"]  # A
    GREEN = device_config.fsm_config["led_colors"]["green"]    # B  
    RED = device_config.fsm_config["led_colors"]["red"]        # C

class LockerController:
    """High-level controller for locker operations"""

    def __init__(self):
        self.max_retries = device_config.fsm_config["max_command_retries"]
        self.retry_delay = device_config.fsm_config["retry_delay"]
        self.mock_mode = device_config.fsm_config["box_virtual"]

    def _get_tempered_status(self, locker_id: int) -> Optional[bool]:
        """
        Get tempered status for a locker from database

        Args:
            locker_id: Locker ID (section_id in database)

        Returns:
            True if tempered, False if not tempered, None if not found or error
        """
        try:
            conn = mysql.connector.connect(
                host=getenv("DB_HOST"),
                port=int(getenv("DB_PORT", 3306)),
                database=getenv("DB_NAME"),
                user=getenv("DB_USER"),
                password=getenv("DB_PASSWORD"),
                autocommit=True
            )

            cursor = conn.cursor()
            query = "SELECT tempered FROM box_sections WHERE section_id = %s LIMIT 1"
            cursor.execute(query, (locker_id,))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            if result:
                return bool(result[0])  # Convert 1/0 to True/False
            else:
                logger.warning(f"No tempered status found for locker {locker_id}")
                return None

        except mysql.connector.Error as err:
            logger.error(f"Database error getting tempered status for locker {locker_id}: {err}")
            return None
        except Exception as e:
            logger.error(f"Exception getting tempered status for locker {locker_id}: {e}")
            return None
        
    async def unlock_locker(self, locker_id: int, is_tempered: bool = False, mode: Optional[str] = None, operator_id: Optional[int] = None, pin_entered: Optional[str] = None, reservation_uuid: Optional[str] = None) -> bool:
        """
        Unlock a specific locker

        Args:
            locker_id: Locker ID (1-206)
            is_tempered: True for tempered lock, False for non-tempered. If None, will be read from database
            mode: Optional mode for timeline logging (storage, product, hygiene, manager, etc.)
            operator_id: Optional operator ID for timeline logging
            pin_entered: Optional PIN entered by user for timeline logging
            reservation_uuid: Optional reservation UUID for timeline logging (used as serial_number)

        Returns:
            True if successful, False otherwise
        """
        # If is_tempered is None, read from database
        if is_tempered is None:
            logger.debug(f"Reading tempered status for locker {locker_id} from database")
            is_tempered = self._get_tempered_status(locker_id)
            if is_tempered is None:
                logger.error(f"Could not determine tempered status for locker {locker_id}, defaulting to False")
                is_tempered = False  # Default to non-tempered if not found
            else:
                logger.debug(f"Locker {locker_id} tempered status: {is_tempered}")

        try:
            if self.mock_mode:      # ELECTRONIC MOCK MODE
                success = True
                result = "mock mode"
            else:
                result = await send_command(
                    str(locker_id), 
                    "unlock", 
                    max_retries=self.max_retries,
                    is_tempered=is_tempered
                )
                success = not result.startswith('-')
            
            if success:
                logger.info(f"Successfully unlocked locker {locker_id}")
                log_timeline_event(
                    serial_number=reservation_uuid,
                    event_type="open_lock",
                    event_result="success",
                    section_id=locker_id,
                    tempered_unlock=is_tempered,
                    message=f"Result code: {result}",
                    mode=mode,
                    operator_id=operator_id,
                    entered_pin=pin_entered
                )
            else:
                logger.error(f"Failed to unlock locker {locker_id}: {result}")
                log_timeline_event(
                    serial_number=reservation_uuid,
                    event_type="open_lock",
                    event_result="failed",
                    section_id=locker_id,
                    tempered_unlock=is_tempered,
                    message=f"Result code: {result}",
                    mode=mode,
                    operator_id=operator_id,
                    entered_pin=pin_entered
                )
            
            return success
            
        except Exception as e:
            logger.error(f"Exception unlocking locker {locker_id}: {e}")
            log_timeline_event(
                serial_number=reservation_uuid,
                event_type="open_lock",
                event_result="failed",
                section_id=locker_id,
                tempered_unlock=is_tempered,
                message=f"Exception unlocking locker with error: {e}",
                mode=mode,
                operator_id=operator_id,
                entered_pin=pin_entered
            )
            return False
    
    async def lock_locker(self, locker_id: int, is_tempered: bool = True, mode: Optional[str] = None, operator_id: Optional[int] = None, reservation_uuid: Optional[str] = None) -> bool:
        """
        Lock a specific locker (only for tempered locks)

        Args:
            locker_id: Locker ID (1-206)
            is_tempered: True for tempered lock, False for non-tempered
            mode: Optional mode for timeline logging (storage, product, hygiene, manager, etc.)

        Returns:
            True if successful, False otherwise
        """
        if not is_tempered:
            # Non-tempered locks don't need explicit locking
            return True
            
        try:
            if self.mock_mode:      # ELECTRONIC MOCK MODE
                success = True
                result = "mock mode"
            else:
                result = await send_command(
                    str(locker_id), 
                    "lock", 
                    max_retries=self.max_retries,
                    is_tempered=is_tempered
                )
                success = not result.startswith('-')
            
            if success:
                logger.info(f"Successfully locked locker {locker_id}")
                log_timeline_event(
                    serial_number=reservation_uuid,
                    event_type="lock",
                    event_result="success",
                    section_id=locker_id,
                    tempered_unlock=is_tempered,
                    message=f"Result code: {result}",
                    mode=mode,
                    operator_id=operator_id
                )
            else:
                logger.error(f"Failed to lock locker {locker_id}: {result}")
                log_timeline_event(
                    serial_number=reservation_uuid,
                    event_type="lock",
                    event_result="failed",
                    section_id=locker_id,
                    tempered_unlock=is_tempered,
                    message=f"Result code: {result}",
                    mode=mode,
                    operator_id=operator_id
                )
                
            return success
            
        except Exception as e:
            logger.error(f"Exception locking locker {locker_id}: {e}")
            log_timeline_event(
                event_type="lock",
                event_result="failed",
                section_id=locker_id,
                tempered_unlock=is_tempered,
                message=f"Exception unlocking locker with error: {e}",
                mode=mode,
                operator_id=operator_id
            )
            return False
    
    async def check_door_state(self, locker_id: int, is_tempered: bool = True) -> Optional[bool]:
        """
        Check if door is open or closed

        Args:
            locker_id: Locker ID (1-206)
            is_tempered: True for tempered lock, False for non-tempered

        Returns:
            True if door is open, False if closed, None if error
        """
        try:
            if self.mock_mode:      # ELECTRONIC MOCK MODE
                # In mock mode, return True (open) - sequence_manager handles the timing logic
                # This represents the door being open after unlock, sequence_manager will handle close timing
                logger.debug(f"Mock mode: Door state for locker {locker_id}: open")
                return True

            result = await send_command(
                str(locker_id),
                "check_door",
                max_retries=self.max_retries,
                is_tempered=is_tempered
            )

            if result.startswith('-'):
                logger.error(f"Failed to check door state for locker {locker_id}: {result}")
                return None

            # Parse result - expecting "1 1" for open, "1 0" for closed
            parts = result.strip().split()
            if len(parts) >= 2:
                door_state = parts[1] == "1"
                logger.debug(f"Door state for locker {locker_id}: {'open' if door_state else 'closed'}")
                return door_state
            else:
                logger.error(f"Invalid door state response for locker {locker_id}: {result}")
                return None

        except Exception as e:
            logger.error(f"Exception checking door state for locker {locker_id}: {e}")
            return None
    
    async def set_led_color(self, led_id: int, color: Optional[str]) -> bool:
        """
        Set LED color for specific LED
        
        Args:
            led_id: LED ID
            color: Color code (A, B, C or None for turning off)
            
        Returns:
            True if successful, False otherwise
        """
        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True

        try:
            if color is None:
                # To turn off LED, we need to exclude it from active LEDs
                # For now, just skip the operation and return success
                logger.debug(f"Skipping LED {led_id} (turning off)")
                return True
            
            command = f"write_led_colors:{led_id}:{color}"
            result = await send_command("", command, max_retries=self.max_retries)
            
            success = not result.startswith('-')
            if success:
                logger.debug(f"Set LED {led_id} to color {color}")
            else:
                logger.error(f"Failed to set LED {led_id} color: {result}")
                
            return success
            
        except Exception as e:
            logger.error(f"Exception setting LED {led_id} color: {e}")
            return False
    
    async def set_multiple_led_colors(self, led_colors: Dict[int, Optional[str]]) -> bool:
        """
        Set multiple LED colors in one command
        
        Args:
            led_colors: Dictionary mapping LED ID to color code (None values are skipped)
            
        Returns:
            True if successful, False otherwise
        """

        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True

        # Filter out None values (LEDs to turn off)
        active_leds = {led_id: color for led_id, color in led_colors.items() if color is not None}
        
        if not active_leds:
            # No LEDs to set, use clear_all_led instead
            return await self.clear_all_leds()
            
        try:
            # Build command: write_led_colors:1:A:2:B:3:C
            command_parts = ["write_led_colors"]
            for led_id, color in active_leds.items():
                command_parts.extend([str(led_id), color])
            
            command = ":".join(command_parts)
            result = await send_command("", command, max_retries=self.max_retries)
            
            success = not result.startswith('-')
            if success:
                logger.info(f"Set multiple LED colors: {active_leds}")
            else:
                logger.error(f"Failed to set multiple LED colors: {result}")
                
            return success
            
        except Exception as e:
            logger.error(f"Exception setting multiple LED colors: {e}")
            return False
    
    async def clear_all_leds(self) -> bool:
        """Clear all LEDs (turn them off)"""
        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True
            
        try:
            result = await send_command("", "disable_led", max_retries=self.max_retries)
            success = not result.startswith('-')
            if success:
                logger.info("All LEDs cleared")
            else:
                logger.error(f"Failed to clear all LEDs: {result}")
            return success
        except Exception as e:
            logger.error(f"Exception clearing all LEDs: {e}")
            return False
    
    async def enable_led_system(self) -> bool:
        """Enable LED system"""
        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True
            
        try:
            result = await send_command("", "enable_led", max_retries=self.max_retries)
            success = not result.startswith('-')
            if success:
                logger.info("LED system enabled")
            else:
                logger.error(f"Failed to enable LED system: {result}")
            return success
        except Exception as e:
            logger.error(f"Exception enabling LED system: {e}")
            return False
    
    async def disable_led_system(self) -> bool:
        """Disable LED system"""
        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True
            
        try:
            result = await send_command("", "disable_led", max_retries=self.max_retries)
            success = not result.startswith('-')
            if success:
                logger.info("LED system disabled")
            else:
                logger.error(f"Failed to disable LED system: {result}")
            return success
        except Exception as e:
            logger.error(f"Exception disabling LED system: {e}")
            return False



    async def unlock_service(self) -> bool:
        """
        Unlock service door
        """
        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True  # Mock mode always returns True for testing

        try:
            result = await send_command("", "unlock_service", max_retries=self.max_retries)
            success = not result.startswith('-')
            if success:
                logger.info("Service door unlocked")
            else:
                logger.error(f"Failed to unlock service door: {result}")
            return success
        except Exception as e:
            logger.error(f"Exception unlocking service door: {e}")
            return False

    async def check_service(self) -> Optional[bool]:
        """
        Check if service door is open or closed

        Returns:
            True if door is open, False if closed, None if error
        """
        if self.mock_mode:      # ELECTRONIC MOCK MODE
            return True  # Mock mode always returns True for testing
        try:
            result = await send_command("", "check_service", max_retries=self.max_retries)
            success = not result.startswith('-')
            if success:
                logger.info(f"Service door state result: {result}")
                # Parse result format: "1 1 2" where second value is door state
                try:
                    parts = result.strip().split()
                    if len(parts) >= 2:
                        door_state = int(parts[1])  # Second value is door state
                        is_open = door_state == 1
                        logger.info(f"Service door is {'open' if is_open else 'closed'}")
                        return is_open
                    else:
                        logger.error(f"Invalid service door state format: {result}")
                        return None
                except (ValueError, IndexError) as e:
                    logger.error(f"Failed to parse service door state '{result}': {e}")
                    return None
            else:
                logger.error(f"Failed to check service door: {result}")
                return None
        except Exception as e:
            logger.error(f"Exception checking service door: {e}")
            return None
