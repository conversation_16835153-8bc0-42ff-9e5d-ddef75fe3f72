-- migrate:up
CREATE TABLE close_totals_transactions (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(255) NOT NULL UNIQUE,
    date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    type VARCHAR(255) NOT NULL,
    response TEXT DEFAULT NULL,
    INDEX close_totals_transactions_uuid_index (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE close_totals_transactions;
